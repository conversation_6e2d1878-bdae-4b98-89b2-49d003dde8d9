import { Types } from 'mongoose';
import logger from '../../common/logger/log.module';
import { connectToMongo, closeConnection, getCollection } from '../../common/database/db.module';
import { parseCSV } from '../../common/utils/csv-parser';
import { MigrationTrackerService } from '../migration/migration-tracker.service';
import { MigrationStatus } from '../migration/migration-tracker.model';
import { User } from '../user/user.model';
import { CreateInvoicePurchaseDto, PurchaseService } from './purchase.service';
import { Facility } from '../facility/facility.model';
import { Pricing } from '../pricing/pricing.model';

interface ICsvInvoice {
  invoiceId: number;
  userId: string;
  facilityId: string;
  invoiceDate: string;
  subTotal: number;
  total: number;
  cartDiscount: number;
  cartDiscountType: string;
  paymentDetails: ICsvPaymentDetail;
  clientBillingDetails: ICsvClientBillingDetail;
  packageId: number;
  quantity: number;
}
interface ICsvInvoice {
  invoiceId: number;
  userId: string;
  facilityId: string;
  invoiceDate: string;
  subTotal: number;
  total: number;
  cartDiscount: number;
  cartDiscountType: string;
  paymentDetails: ICsvPaymentDetail;
  clientBillingDetails: ICsvClientBillingDetail;
  packageId: number;
  quantity: number;
}

interface ICsvPurchaseItem {
  invoiceId: number;
  packageId: string;
  quantity: number;
  isBundledPricing?: boolean;
}

interface ICsvProductItem {
  invoiceId: number;
  productId: string;
  productVariantId?: string;
  quantity: number;
}

interface ICsvCustomPackageItem {
  invoiceId: number;
  customPackageId: string;
  quantity: number;
}

interface ICsvPaymentDetail {
  invoiceId: number;
  paymentMethod: string;
  paymentMethodId?: string;
  paymentGateway?: string;
  transactionId?: string;
  amount: number;
  paymentDate: string;
  paymentStatus?: string;
  description?: string;
}

interface ICsvClientDetail {
  invoiceId: number;
  customerId: string;
  name: string;
  email?: string;
  phone?: string;
}

interface ICsvClientBillingDetail {
  invoiceId: number;
  customerId: string;
  name: string;
  addressLine1?: string;
  addressLine2?: string;
  postalCode?: number;
  cityId?: string;
  cityName?: string;
  stateId: string;
  stateName: string;
  gstNumber?: string;
  utCode: string;
}

interface ICsvBillingDetail {
  invoiceId: number;
  facilityName: string;
  billingName: string;
  gstNumber?: string;
  email: string;
  phone: string;
  addressLine1: string;
  addressLine2?: string;
  postalCode?: number;
  cityId: string;
  cityName: string;
  stateId: string;
  stateName: string;
  utCode: string;
}

/**
 * Validate invoice data
 * @param invoice Invoice data to validate
 * @returns Array of validation errors
 */
function validateInvoiceData(invoice): string[] {
  const errors: string[] = [];

  if (!invoice.invoiceId) errors.push('Invoice ID is required');
  if (!invoice.userId) errors.push('User ID is required');
  if (!invoice.facilityId) errors.push('Facility ID is required');
  if (!invoice.invoiceDate) errors.push('Invoice date is required');
  if (invoice.subTotal === undefined) errors.push('Subtotal is required');
  if (invoice.total === undefined) errors.push('Total is required');
  if (invoice.cartDiscount === undefined) errors.push('Cart discount is required');
  if (!invoice.cartDiscountType) errors.push('Cart discount type is required');
  if (!invoice.paymentDetails) errors.push('Payment details are required');
  if (!invoice.clientBillingDetails) errors.push('Client billing details are required');

  return errors;
}

async function getAllUsers(ids: string[]) {
  const _id: Types.ObjectId[] = []
  const id: string[] = []

  ids.forEach(idstr => {
    if(Types.ObjectId.isValid(`${idstr}`)){
      _id.push(new Types.ObjectId(idstr));
      return;
    };
    id.push(idstr);
  });
  const users = await User.find({
    $or: [
      { _id: { $in: _id } },
      { id: { $in: id } },
    ]
  }, { _id: 1, id: 1 }).exec();
  return users;
}

async function getAllFacility(params:any[]) {
  const _id: Types.ObjectId[] = []
  const id: string[] = []

  params.forEach(idstr => {
    if(Types.ObjectId.isValid(`${idstr}`)){
      _id.push(new Types.ObjectId(idstr));
      return;
    };
    id.push(idstr);
  });
  const facilities = await Facility.find({
    $or: [
      { _id: { $in: _id } },
      { id: { $in: id } },
    ]
    }, { _id: 1, id: 1 }).exec();
  return facilities;

}

async function getAllPricing(params:any[]) {
    const _id: Types.ObjectId[] = []
    const id: string[] = []
  
    params.forEach(idstr => {
      if(Types.ObjectId.isValid(`${idstr}`)){
        _id.push(new Types.ObjectId(idstr));
        return;
      };
      id.push(idstr);
    });
    const pricings = await Pricing.find({
      $or: [
        { _id: { $in: _id } },
        { id: { $in: id } },
      ]
      }, { _id: 1, id: 1 }).exec();

    return pricings;
}

async function getAllPaymentMethod(params:any[]) {
  const shortId: string[] = params
  const PaymentMethodModel = await getCollection('paymentmethods');
  const paymentMethods = await PaymentMethodModel.find({
    shortId: { $in: shortId }
    }).toArray();
  return paymentMethods;
}


/**
 * Migrate invoices data from CSV to MongoDB
 * @param dbName Database name
 */
export async function migrateInvoices(dbName: string = 'hop-migration'): Promise<void> {
  let session: any = null;
  let tracker: any = null;

  try {
    logger.log('Starting invoices migration...');

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require('mongoose');
    session = await mongoose.startSession();
    session.startTransaction();


    try {
      // Get invoices data from CSV
      const csvInvoicesRow = await parseCSV<ICsvInvoice>('data/invoices.csv');
      const csvInvoices = csvInvoicesRow.map((invoice: ICsvInvoice) => ({
        ...invoice,
        facilityId: Number(invoice.facilityId),
        invoiceId: Number(invoice.invoiceId),
        subTotal: Number(invoice.subTotal),
        total: Number(invoice.total),
        cartDiscount: Number(invoice.cartDiscount),
        paymentDetails: {
          ...invoice.paymentDetails,
          invoiceId: Number(invoice.paymentDetails.invoiceId),
          amount: Number(invoice.paymentDetails.amount),
          paymentDate: new Date(invoice.paymentDetails.paymentDate)
        },
        clientBillingDetails: {
          ...invoice.clientBillingDetails,
          invoiceId: Number(invoice.clientBillingDetails.invoiceId),
          postalCode: invoice.clientBillingDetails.postalCode ? Number(invoice.clientBillingDetails.postalCode) : undefined
        }
      }));

      // Validate data
      const validationErrors: { [key: string]: string[] } = {};
      csvInvoices.forEach((invoice, index) => {
        const errors = validateInvoiceData({
          ...invoice,
          paymentDetails: invoice.paymentDetails
        });
        if (errors.length > 0) {
          validationErrors[`Row ${index + 1}`] = errors;
        }
      });

      if (Object.keys(validationErrors).length > 0) {
        logger.error('Validation errors in invoices data:', validationErrors);
        throw new Error('Invalid invoices data in CSV');
      }

      const userIds = [...new Set(csvInvoices.map(invoice => invoice.userId))];
      const facilityIds = [...new Set(csvInvoices.map(invoice => invoice.facilityId))];
      const packageIds = [...new Set(csvInvoices.map(invoice => invoice.packageId))];
      const paymentMethodIds = [...new Set(csvInvoices.map(invoice => invoice.paymentDetails.paymentMethod))];

      const [users, facilities, pricings, paymentMethods] = await Promise.all([
        getAllUsers(userIds),
        getAllFacility(facilityIds),
        getAllPricing(packageIds),
        getAllPaymentMethod(paymentMethodIds)
      ]);

      const userMap = new Map();
      const facilityMap: Map<string, Types.ObjectId> = new Map();
      const pricingMap: Map<string, Types.ObjectId> = new Map();
      const paymentMethodMap: Map<string, Types.ObjectId> = new Map();

      for (const user of users) {
        userMap.set(user._id.toString(), user._id);
        userMap.set(user.id.toString(), user._id);
      }
      
      for (const facility of facilities) {
        facilityMap.set(facility._id.toString(), facility._id as Types.ObjectId);
        facilityMap.set(facility.id.toString(), facility._id as Types.ObjectId);
      }

      for (const pricing of pricings) {
        pricingMap.set(pricing._id.toString(), pricing._id  as Types.ObjectId);
        pricingMap.set(pricing.id.toString(), pricing._id as Types.ObjectId);
      }

      for (const paymentMethod of paymentMethods) {
        paymentMethodMap.set(paymentMethod.shortId, paymentMethod._id);
      }

      // Process and insert invoices
      const errors: string[] = [];
      const purchaseService = new PurchaseService();
      const createPurchaseBodyObject: Record<number, CreateInvoicePurchaseDto> = {}

      for (const invoice of csvInvoices) {
        let userId = userMap.get(invoice.userId.toString());
        if(!userId){
          logger.error(`User not found for invoice ${invoice.invoiceId}`);
          throw new Error(`User not found for invoice ${invoice.invoiceId}`);
        }

        let facilityId = facilityMap.get(invoice.facilityId.toString());
        if(!facilityId){
          logger.error(`Facility not found for invoice ${invoice.invoiceId}`);
          throw new Error(`Facility not found for invoice ${invoice.invoiceId}`);
        }
        
        let packageId = pricingMap.get(invoice.packageId.toString());
        if(!packageId){
          logger.error(`Package not found for invoice ${invoice.invoiceId}`);
          throw new Error(`Package not found for invoice ${invoice.invoiceId}`);
        }

        let paymentMethodId = paymentMethodMap.get(invoice.paymentDetails.paymentMethod);
        if(!paymentMethodId){
          logger.error(`Payment method not found for invoice ${invoice.invoiceId}`);
          throw new Error(`Payment method not found for invoice ${invoice.invoiceId}`);
        }

        const createPurchaseBody: CreateInvoicePurchaseDto = {
          invoiceId: invoice.invoiceId,
          userId: userId,
          facilityId: facilityId,
          purchaseItems: [{
            packageId: packageId,
            quantity: invoice.quantity,
          }],
          productsItem: [],
          customPackageItems: [],
          subTotal: invoice.subTotal,
          total: invoice.total,
          cartDiscount: invoice.cartDiscount,
          cartDiscountType: invoice.cartDiscountType,
          paymentDetails: [{
            paymentMethod: invoice.paymentDetails.paymentMethod,
            paymentMethodId: paymentMethodId,
            transactionId: invoice.paymentDetails.transactionId,
            amount: invoice.paymentDetails.amount,
            paymentDate: new Date(invoice.paymentDetails.paymentDate),
            paymentStatus: invoice.paymentDetails.paymentStatus,
            paymentGateway: invoice.paymentDetails.paymentGateway,
          }],
          invoiceDate: new Date(invoice.invoiceDate),
          platform: 'web',
        };
        if(createPurchaseBodyObject[invoice.invoiceId]){
          createPurchaseBodyObject[invoice.invoiceId] = {
            ...createPurchaseBodyObject[invoice.invoiceId],
            productsItem: [
              ...createPurchaseBodyObject[invoice.invoiceId].productsItem,
              ...createPurchaseBody.productsItem
            ]
          }
        } else {
          createPurchaseBodyObject[invoice.invoiceId] = createPurchaseBody;
        }
      }

      for (const createPurchaseBody of Object.values(createPurchaseBodyObject)) {
        try {
          await purchaseService.createPurchase(createPurchaseBody, session);
        } catch (error) {
          logger.error(`Error processing row ${createPurchaseBody.invoiceId}:`, error);
          errors.push(`Error processing row ${createPurchaseBody.invoiceId}: ${error.message}`);
        }
      }

      if (errors.length > 0) {
        logger.error('Errors in migration:', errors);
        throw new Error('Errors in migration');
      }

      // Commit the transaction
      await session.commitTransaction();
      logger.log('Transaction committed successfully');

    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }

      // Update migration tracker with failure
      if (tracker) {
        await MigrationTrackerService.completeMigration(
          tracker._id.toString(),
          MigrationStatus.FAILED
        );
      }

      logger.error('Error in migration process:', error);
      throw error;
    }
  } catch (error) {
    logger.error('Error migrating invoices:', error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}
