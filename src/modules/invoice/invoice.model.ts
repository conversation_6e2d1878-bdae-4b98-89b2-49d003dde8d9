import mongoose, { Document, Schema, Types } from 'mongoose';
import { DiscountType } from '../pricing/pricing.model';
import { DurationUnit } from '../pricing/pricing.model';
import { PaymentStatus } from '../../utils/enums/payment.enum';
import { InvoiceStatus } from '../../utils/enums/invoice-status.enum';
import { PaymentMethod } from '../../utils/enums/paymentMethod.enum';

export interface IPurchaseItem {
  packageId: Types.ObjectId;
  packageName: string;
  quantity: number;
  isBundledPricing?: boolean;
  expireIn: number;
  durationUnit: DurationUnit;
  startDate: Date;
  endDate: Date;
  unitPrice: number;
  discountType?: DiscountType;
  discountValue?: number;
  discountedBy?: Types.ObjectId;
  discountExcludeCart: number;
  discountIncludeCart: number;
  hsnOrSacCode?: string;
  tax: number;
  gstAmount: number;
}

export interface IProductItem {
  productId: Types.ObjectId;
  productVariantId?: Types.ObjectId;
  productName: string;
  quantity: number;
  salePrice: number;
  mrp: number;
  finalPrice: number;
  discountType?: string;
  discountValue?: number;
  discountedBy?: Types.ObjectId;
  discountExcludeCart: number;
  discountIncludeCart: number;
  hsnOrSacCode?: string;
  tax: number;
  gstAmount: number;
}

export interface ICustomPackageItem {
  customPackageId: Types.ObjectId;
  packageName: string;
  quantity: number;
  unitPrice: number;
  discountType?: DiscountType;
  discountValue?: number;
  discountExcludeCart: number;
  discountIncludeCart: number;
  hsnOrSacCode?: string;
  tax: number;
  gstAmount: number;
}

export interface IPaymentDetail {
  paymentMethod: string;
  paymentMethodId: Types.ObjectId;
  paymentGateway?: string;
  transactionId?: string;
  amount: number;
  paymentDate: Date;
  paymentStatus?: PaymentStatus;
  description?: string;
  denominations?: Record<number, number>;
}

export interface IClientDetails {
  customerId: string;
  name: string;
  email?: string;
  phone?: string;
}

export interface IClientBillingDetails {
  customerId: string;
  name: string;
  addressLine1?: string;
  addressLine2?: string;
  postalCode?: number;
  cityId?: Types.ObjectId;
  cityName?: string;
  stateId: Types.ObjectId;
  stateName: string;
  gstNumber?: string;
  utCode: string;
}

export interface IBillingDetails {
  facilityName: string;
  billingName: string;
  gstNumber?: string;
  email: string;
  phone: string;
  addressLine1: string;
  addressLine2?: string;
  postalCode?: number;
  cityId: Types.ObjectId;
  cityName: string;
  stateId: Types.ObjectId;
  stateName: string;
  utCode: string;
}

export interface IInvoice extends Document {
  createdBy: Types.ObjectId;
  cancelledBy?: Types.ObjectId;
  invoiceDate: Date;
  invoiceNumber: number;
  orderId: number;
  userId: Types.ObjectId;
  organizationId: Types.ObjectId;
  facilityId: Types.ObjectId;
  purchaseItems: IPurchaseItem[];
  productItem: IProductItem[];
  customPackageItems: ICustomPackageItem[];
  subTotal: number;
  discount?: number;
  discountedBy?: Types.ObjectId;
  cartDiscount?: number;
  cartDiscountAmount?: number;
  cartDiscountType?: string;
  totalGstValue: number;
  totalAmountAfterGst: number;
  roundOff: number;
  grandTotal: number;
  amountPaid?: number;
  amountInWords: string;
  paymentStatus: PaymentStatus;
  paymentReason?: string;
  paymentDetails: IPaymentDetail[];
  isSplittedPayment: boolean;
  platform?: string;
  invoiceStatus?: string;
  refundStatus?: string;
  refundAmount?: number;
  invoiceFilePath?: string;
  clientDetails: IClientDetails;
  clientBillingDetails: IClientBillingDetails;
  billingDetails: IBillingDetails;
  isForBusiness: boolean;
  paymentBy?: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Define schemas for nested objects
const PurchaseItemSchema = new Schema<IPurchaseItem>({
  packageId: { type: Schema.Types.ObjectId, required: true, ref: "Pricing" },
  packageName: { type: String, required: true },
  quantity: { type: Number, required: true, default: 1 },
  isBundledPricing: { type: Boolean, required: false },
  expireIn: { type: Number, required: true },
  durationUnit: { type: String, required: true, enum: Object.values(DurationUnit) },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  unitPrice: { type: Number, required: true },
  discountType: { type: String, required: false, enum: Object.values(DiscountType) },
  discountedBy: { type: Schema.Types.ObjectId, required: false, default: null },
  discountValue: { type: Number, required: false },
  discountExcludeCart: { type: Number, required: true },
  discountIncludeCart: { type: Number, required: true },
  hsnOrSacCode: { type: String, required: false },
  tax: { type: Number, required: true, default: 0 },
  gstAmount: { type: Number, required: true, default: 0 },
}, { _id: false });

const ProductItemSchema = new Schema<IProductItem>({
  productId: { type: Schema.Types.ObjectId, required: true, ref: 'Product' },
  productVariantId: { type: Schema.Types.ObjectId, required: false, ref: 'ProductVariant' },
  productName: { type: String, required: true },
  quantity: { type: Number, required: true, default: 1 },
  salePrice: { type: Number, required: true },
  mrp: { type: Number, required: true },
  finalPrice: { type: Number, required: true },
  discountType: { type: String, required: false, default: "percentage" },
  discountValue: { type: Number, required: false },
  discountedBy: { type: Schema.Types.ObjectId, required: false, default: null },
  discountExcludeCart: { type: Number, required: true },
  discountIncludeCart: { type: Number, required: true },
  hsnOrSacCode: { type: String, required: false },
  tax: { type: Number, required: true, default: 0 },
  gstAmount: { type: Number, required: true, default: 0 },
}, { _id: false });

const CustomPackageItemSchema = new Schema<ICustomPackageItem>({
  customPackageId: { type: Schema.Types.ObjectId, required: true, ref: "CustomPackage" },
  packageName: { type: String, required: true },
  quantity: { type: Number, required: true, default: 1 },
  unitPrice: { type: Number, required: true },
  discountType: { type: String, required: false, enum: Object.values(DiscountType) },
  discountValue: { type: Number, required: false },
  discountExcludeCart: { type: Number, required: true },
  discountIncludeCart: { type: Number, required: true },
  hsnOrSacCode: { type: String, required: false },
  tax: { type: Number, required: true, default: 0 },
  gstAmount: { type: Number, required: true, default: 0 },
}, { _id: false });

const PaymentDetailSchema = new Schema<IPaymentDetail>({
  paymentMethod: { type: String, required: true },
  paymentMethodId: { type: Schema.Types.ObjectId, required: true, ref: "Facility.paymentMethodId" },
  paymentGateway: { type: String, required: false },
  transactionId: { type: String, required: false },
  amount: { type: Number, required: true },
  paymentDate: { type: Date, required: true },
  paymentStatus: { type: String, required: false, enum: Object.values(PaymentStatus) },
  description: { type: String, required: false },
  denominations: {
    type: Map,
    of: Number,
    required: function (this: any) { return this.paymentMethod === PaymentMethod.CASH; },
  },
}, { _id: false });

const ClientDetailsSchema = new Schema<IClientDetails>({
  customerId: { type: String, required: true },
  name: { type: String, required: true },
  email: { type: String, required: false },
  phone: { type: String, required: false },
}, { _id: false });

const ClientBillingDetailsSchema = new Schema<IClientBillingDetails>({
  customerId: { type: String, required: true },
  name: { type: String, required: true },
  addressLine1: { type: String, required: false },
  addressLine2: { type: String, required: false },
  postalCode: { type: Number, required: false },
  cityId: { type: Schema.Types.ObjectId, ref: "Cities", required: false },
  cityName: { type: String, required: false },
  stateId: { type: Schema.Types.ObjectId, ref: "State", required: true },
  stateName: { type: String, required: true },
  gstNumber: { type: String, required: function (this: any) { return this.isForBusiness === true; }, default: "" },
  utCode: { type: String, required: true },
}, { _id: false });

const BillingDetailsSchema = new Schema<IBillingDetails>({
  facilityName: { type: String, required: true },
  billingName: { type: String, required: true },
  gstNumber: { type: String, required: false },
  email: { type: String, required: true },
  phone: { type: String, required: true },
  addressLine1: { type: String, required: false },
  addressLine2: { type: String, required: false },
  postalCode: { type: Number, required: false },
  cityId: { type: Schema.Types.ObjectId, ref: "Cities", required: true },
  cityName: { type: String, required: true },
  stateId: { type: Schema.Types.ObjectId, ref: "State", required: true },
  stateName: { type: String, required: true },
  utCode: { type: String, required: true },
}, { _id: false });

// Define the main Invoice schema
const InvoiceSchema = new Schema<IInvoice>({
  createdBy: { type: Schema.Types.ObjectId, required: true, ref: "User" },
  cancelledBy: { type: Schema.Types.ObjectId, required: false, ref: 'User' },
  invoiceDate: { type: Date, required: true, default: Date.now },
  invoiceNumber: { type: Number, required: true },
  orderId: { type: Number, required: true },
  userId: { type: Schema.Types.ObjectId, required: true, ref: "User" },
  organizationId: { type: Schema.Types.ObjectId, required: true, ref: "User" },
  facilityId: { type: Schema.Types.ObjectId, required: true, ref: "Facility" },
  purchaseItems: { type: [PurchaseItemSchema], required: true },
  productItem: { type: [ProductItemSchema], required: true },
  customPackageItems: { type: [CustomPackageItemSchema], required: true },
  subTotal: { type: Number, required: true },
  discount: { type: Number, required: false, default: 0 },
  discountedBy: { type: Schema.Types.ObjectId, required: false, default: null, ref: "User" },
  cartDiscount: { type: Number, required: false, default: 0 },
  cartDiscountAmount: { type: Number, required: false, default: 0 },
  cartDiscountType: { type: String, required: false },
  totalGstValue: { type: Number, required: false, default: 0 },
  totalAmountAfterGst: { type: Number, required: false, default: 0 },
  roundOff: { type: Number, required: false, default: 0 },
  grandTotal: { type: Number, required: true },
  amountPaid: { type: Number, required: false },
  amountInWords: { type: String, required: true },
  paymentStatus: { type: String, required: true, enum: Object.values(PaymentStatus), default: PaymentStatus.COMPLETED },
  paymentReason: { type: String, required: false },
  paymentDetails: { type: [PaymentDetailSchema], required: false },
  isSplittedPayment: { type: Boolean, required: false, default: false },
  platform: { type: String, required: false },
  invoiceStatus: { type: String, required: false, default: InvoiceStatus.PENDING },
  refundStatus: { type: String, required: false },
  refundAmount: { type: Number, required: false },
  invoiceFilePath: { type: String, required: false },
  clientDetails: { type: ClientDetailsSchema, required: true },
  clientBillingDetails: { type: ClientBillingDetailsSchema, required: true },
  billingDetails: { type: BillingDetailsSchema, required: true },
  isForBusiness: { type: Boolean, default: false, required: false },
  paymentBy: { type: Schema.Types.ObjectId, default: null, required: false },
}, { timestamps: true });

// Create and export the model
export const INVOICE_COLLECTION = 'invoices';
export const Invoice = mongoose.model<IInvoice>('Invoice', InvoiceSchema, INVOICE_COLLECTION);
