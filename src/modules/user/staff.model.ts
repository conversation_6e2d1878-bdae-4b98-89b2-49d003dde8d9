import mongoose, { Document, Schema } from "mongoose";
import { Gender } from "../../common/enums/enums";

// Define interface for embedded address document
interface IAddress {
  stateId?: mongoose.Types.ObjectId;
  cityId?: mongoose.Types.ObjectId;
  street?: string;
  country?: string;
}

// Main staff interface
export interface IStaff extends Document {
  createdBy?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  organizationId: mongoose.Types.ObjectId;
  facilityId: mongoose.Types.ObjectId[];
  staffId?: string;
  profilePicture?: string;
  gender?: string;
  pin?: string;
  dateOfBirth?: Date;
  address?: IAddress;
  certification?: string;
  experience?: string;
  description?: string;
  setUpDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Create schema for embedded address document
const AddressSchema = new Schema({
  stateId: { type: Schema.Types.ObjectId, default: "" },
  cityId: { type: Schema.Types.ObjectId, default: "" },
  street: { type: String, default: "" },
  country: { type: String, default: "" }
}, { _id: false });

// Main staff schema
const StaffSchema = new Schema({
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: "User" 
  },
  userId: { 
    type: Schema.Types.ObjectId, 
    required: true, 
    ref: "User" 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    required: true, 
    ref: "User" 
  },
  facilityId: [{ 
    type: Schema.Types.ObjectId, 
    required: true, 
    ref: "Facility" 
  }],
  staffId: { 
    type: String 
  },
  profilePicture: { 
    type: String 
  },
  gender: { 
    type: String, 
    enum: Object.values(Gender) 
  },
  pin: { 
    type: String 
  },
  dateOfBirth: { 
    type: Date 
  },
  address: { 
    type: AddressSchema, 
    default: {} 
  },
  certification: { 
    type: String 
  },
  experience: { 
    type: String 
  },
  description: { 
    type: String 
  },
  setUpDate: { 
    type: Date 
  }
}, { 
  timestamps: true 
});

export const STAFF_COLLECTION = 'staffprofiledetails';
export const Staff = mongoose.model<IStaff>('Staff', StaffSchema, STAFF_COLLECTION);
