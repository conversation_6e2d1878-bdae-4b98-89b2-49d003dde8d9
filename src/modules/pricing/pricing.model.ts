import mongoose, { Document, Schema, Types } from 'mongoose';

// Define enums
export enum ClassType {
    PERSONAL_APPOINTMENT = "personalAppointment",
    CLASSES = "classes",
    BOOKINGS = "bookings",
    COURSES = "courses",
}

export enum DiscountType {
  FLAT = 'Flat',
  PERCENTAGE = 'Percentage'
}

export enum SessionType {
    SINGLE = "single",
    MULTIPLE = "multiple",
    UNLIMITED = "unlimited",
    DAY_PASS = 'day_pass'
}

export enum DurationUnit {
  DAYS = 'days',
  WEEKS = 'weeks',
  MONTHS = 'months',
  YEARS = 'years'
}

// Define interfaces
export interface IActiveTimeFrame {
  startTime: Date;
  endTime: Date;
}

export interface IDiscount {
  type?: DiscountType;
  value?: number;
}

export interface IRelationship {
  serviceCategory?: Types.ObjectId;
  subTypeIds?: Types.ObjectId[];
}

export interface IServices {
  type: ClassType;
  serviceCategory: Types.ObjectId;
  appointmentType?: Types.ObjectId[];
  sessionType: SessionType;
  sessionCount: number;
  dayPassLimit: number;
  sessionPerDay?: number;
  introductoryOffer?: string;
  revenueCategory?: string;
  relationShip?: IRelationship[];
}

export interface IPricing extends Document {
  createdBy: Types.ObjectId;
  organizationId: Types.ObjectId;
  name: string;
  price: number;
  isSellOnline: boolean;
  tax: number;
  services?: IServices;
  expiredInDays: number;
  hsnOrSacCode?: string;
  durationUnit: string;
  membershipId?: Types.ObjectId;
  discount?: IDiscount;
  promotion?: Types.ObjectId;
  isActive: boolean;
  pricingIds?: Types.ObjectId[];
  isBundledPricing: boolean;
  revenueCategory?: string;
  activeTimeFrames?: IActiveTimeFrame[];
  createdAt: Date;
  updatedAt: Date;
}

// Define schemas
const ActiveTimeFrameSchema = new Schema<IActiveTimeFrame>({
  startTime: { type: Date },
  endTime: { type: Date }
}, { _id: false });

const DiscountSchema = new Schema<IDiscount>({
  type: { 
    type: String, 
    enum: Object.values(DiscountType),
    required: false 
  },
  value: { 
    type: Number,
    required: false 
  }
}, { _id: false });

const ServicesSchema = new Schema<IServices>({
  type: { 
    type: String, 
    enum: Object.values(ClassType),
    required: true 
  },
  serviceCategory: { 
    type: Schema.Types.ObjectId, 
    ref: "ServiceCategory", 
    required: true 
  },
  appointmentType: { 
    type: [Schema.Types.ObjectId], 
    ref: "AppointmentType", 
    default: [] 
  },
  sessionType: { 
    type: String, 
    enum: Object.values(SessionType),
    required: true 
  },
  sessionCount: { 
    type: Number, 
    required: true 
  },
  dayPassLimit: { 
    type: Number, 
    required: true 
  },
  sessionPerDay: { 
    type: Number,
    required: false 
  },
  introductoryOffer: { 
    type: String,
    required: false 
  },
  revenueCategory: { 
    type: String,
    required: false 
  },
  relationShip: [
    {
      serviceCategory: { 
        type: Schema.Types.ObjectId, 
        ref: "ServiceCategory",
        required: false 
      },
      subTypeIds: { 
        type: [Schema.Types.ObjectId], 
        ref: "AppointmentType",
        required: false 
      }
    }
  ]
}, { _id: false });

// Define Pricing schema
const PricingSchema = new Schema<IPricing>({

  id: { 
    type: Number, 
    required: false 
  },

  createdBy: { 
    type: Schema.Types.ObjectId, 
    required: true, 
    ref: "User" 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    required: true, 
    ref: "Organization" 
  },
  name: { 
    type: String, 
    required: true 
  },
  price: { 
    type: Number, 
    required: true 
  },
  isSellOnline: { 
    type: Boolean, 
    required: true 
  },
  tax: { 
    type: Number, 
    required: true 
  },
  services: { 
    type: ServicesSchema,
    required: false 
  },
  expiredInDays: { 
    type: Number, 
    required: true 
  },
  hsnOrSacCode: { 
    type: String,
    required: false 
  },
  durationUnit: { 
    type: String, 
    required: true,
    enum: Object.values(DurationUnit)
  },
  membershipId: { 
    type: Schema.Types.ObjectId,
    required: false 
  },
  discount: { 
    type: DiscountSchema,
    required: false 
  },
  promotion: { 
    type: Schema.Types.ObjectId, 
    ref: "Promotion", 
    default: null,
    required: false 
  },
  isActive: { 
    type: Boolean, 
    default: true 
  },
  pricingIds: { 
    type: [Schema.Types.ObjectId], 
    ref: "Pricing",
    required: false 
  },
  isBundledPricing: { 
    type: Boolean, 
    default: false 
  },
  revenueCategory: { 
    type: String,
    required: false,
    trim: true,
  },
  activeTimeFrames: { 
    type: [ActiveTimeFrameSchema], 
    default: [],
    required: false 
  }
}, { 
  timestamps: true 
});

// Create and export the model
export const PRICING_COLLECTION = 'pricings';
export const Pricing = mongoose.model<IPricing>('Pricing', PricingSchema, PRICING_COLLECTION);
