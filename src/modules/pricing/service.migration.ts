import { Types } from "mongoose";
import logger from "../../common/logger/log.module";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { Service } from "./service.model";
import { parseCSV } from "../../common/utils/csv-parser";
import { ClassType, SessionType } from "./pricing.model";

interface ICsvService {
  id: string;
  name: string;
  type: string;
  serviceCategory: string;
  sessionType: string;
  sessionCount: number;
  dayPassLimit: number;
  sessionPerDay: number;
  introductoryOffer?: string;
}

/**
 * Validate service data from CSV
 * @param csvService Service data from CSV
 * @returns Array of validation error messages
 */
function validateServiceData(csvService: ICsvService): string[] {
  const errors: string[] = [];

  if (!csvService.id) {
    errors.push("Service ID is required");
  }

  if (!csvService.name) {
    errors.push("Service name is required");
  }

  if (!csvService.type) {
    errors.push("Service type is required");
  } else if (!Object.values(ClassType).includes(csvService.type as ClassType)) {
    errors.push(`Invalid service type: ${csvService.type}. Valid values are: ${Object.values(ClassType).join(', ')}`);
  }

  if (!csvService.serviceCategory) {
    errors.push("Service category is required");
  }

  if (!csvService.sessionType) {
    errors.push("Session type is required");
  } else if (!Object.values(SessionType).includes(csvService.sessionType as SessionType)) {
    errors.push(`Invalid session type: ${csvService.sessionType}. Valid values are: ${Object.values(SessionType).join(', ')}`);
  }

  if (csvService.sessionCount === undefined || isNaN(csvService.sessionCount) || csvService.sessionCount < 0) {
    errors.push("Session count must be a valid non-negative number");
  }

  if (csvService.dayPassLimit === undefined || isNaN(csvService.dayPassLimit) || csvService.dayPassLimit < 0) {
    errors.push("Day pass limit must be a valid non-negative number");
  }

  if (csvService.sessionPerDay === undefined || isNaN(csvService.sessionPerDay) || csvService.sessionPerDay < 0) {
    errors.push("Session per day must be a valid non-negative number");
  }

  return errors;
}

/**
 * Migrate services data from CSV to MongoDB
 */
export async function migrateServices(_dbName: string = "hop-migration"): Promise<void> {
  let session: any = null;

  try {
    logger.log("Starting services migration...");

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require("mongoose");
    session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get services data from CSV
      const csvServices = await parseCSV<ICsvService>("data/services-category.csv");

      // Validate data
      const validationErrors: { [key: string]: string[] } = {};
      csvServices.forEach((service, index) => {
        const errors = validateServiceData(service);
        if (errors.length > 0) {
          validationErrors[`Row ${index + 1}`] = errors;
        }
      });

      if (Object.keys(validationErrors).length > 0) {
        logger.error("Validation errors in services data:", validationErrors);
        throw new Error("Invalid services data in CSV");
      }

      // Get appointment types data to include during service creation
      const csvAppointmentTypes = await parseCSV<any>("data/appointment-types.csv");

      // Group appointment types by service ID
      const appointmentTypesByServiceId: { [serviceId: string]: any[] } = {};
      csvAppointmentTypes.forEach((appointmentType: any) => {
        if (!appointmentTypesByServiceId[appointmentType.serviceId]) {
          appointmentTypesByServiceId[appointmentType.serviceId] = [];
        }
        appointmentTypesByServiceId[appointmentType.serviceId].push({
          name: appointmentType.name,
          durationInMinutes: appointmentType.durationInMinutes,
          onlineBookingAllowed: true,
          isActive: appointmentType.isActive !== undefined ? appointmentType.isActive : true,
          image: "",
          isFeatured: false
        });
      });

      // Transform data for MongoDB
      const services = csvServices.map((csvService) => {
        const serviceAppointmentTypes = appointmentTypesByServiceId[csvService.id] || [];

        return {
          name: csvService.name,
          description: `${csvService.serviceCategory} - ${csvService.sessionType}`,
          image: "",
          classType: csvService.type as ClassType,
          appointmentType: serviceAppointmentTypes, // Include appointment types during creation
          isFeatured: false,
          createdBy: new Types.ObjectId(global.config.organization),
          organizationId: new Types.ObjectId(global.config.organization)
        };
      });

      // Insert services
      const result = await Service.insertMany(services, { session });
      logger.log(`Successfully migrated ${result.length} services to MongoDB`);

      // Log appointment types added to each service
      result.forEach((service, index) => {
        const csvService = csvServices[index];
        const appointmentTypesCount = service.appointmentType.length;
        logger.log(`Service "${service.name}" (CSV ID: ${csvService.id}) created with ${appointmentTypesCount} appointment types`);

        if (appointmentTypesCount > 0) {
          service.appointmentType.forEach((apt: any, aptIndex: number) => {
            logger.log(`  ${aptIndex + 1}. ${apt.name} (${apt.durationInMinutes} min)`);
          });
        }
      });

      // Create a mapping of CSV ID to MongoDB ObjectId for reference
      const serviceIdMapping: { [csvId: string]: string } = {};
      result.forEach((service, index) => {
        serviceIdMapping[csvServices[index].id] = service._id.toString();
      });

      logger.log("Service ID mapping (CSV ID -> MongoDB ObjectId):");
      Object.entries(serviceIdMapping).forEach(([csvId, mongoId]) => {
        logger.log(`  ${csvId} -> ${mongoId}`);
      });

      // Commit the transaction
      await session.commitTransaction();
      logger.log("Transaction committed successfully");
    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }
      logger.error("Error in migration process:", error);
      throw error;
    }
  } catch (error) {
    logger.error("Error migrating services:", error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}
