import mongoose, { Document, Schema, Types } from 'mongoose';
import { ClassType } from './pricing.model';

// Define interfaces
export interface IAppointmentType {
  name: string;
  durationInMinutes: number;
  onlineBookingAllowed: boolean;
  isActive: boolean;
  image?: string;
  isFeatured?: boolean;
}

export interface IService extends Document {
  organizationId: Types.ObjectId;
  name: string;
  description: string;
  image?: string;
  classType: ClassType;
  appointmentType: IAppointmentType[];
  createdBy: Types.ObjectId;
  isFeatured?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Define appointment type schema
const AppointmentTypeSchema = new Schema<IAppointmentType>({
  name: { type: String, required: true },
  durationInMinutes: { type: Number, required: true },
  onlineBookingAllowed: { type: Boolean, required: true },
  isActive: { type: Boolean, default: true },
  image: { type: String, required: false },
  isFeatured: { type: Boolean, default: false }
}, { 
  timestamps: false 
});

// Define service schema
const ServiceSchema = new Schema<IService>({
  organizationId: { 
    type: Schema.Types.ObjectId, 
    required: true, 
    ref: "User" 
  },
  name: { 
    type: String, 
    required: true 
  },
  description: { 
    type: String, 
    required: false, 
    default: "" 
  },
  image: { 
    type: String, 
    required: false 
  },
  classType: { 
    type: String, 
    required: true, 
    enum: Object.values(ClassType) 
  },
  appointmentType: [AppointmentTypeSchema],
  createdBy: { 
    type: Schema.Types.ObjectId, 
    required: true, 
    ref: "User" 
  },
  isFeatured: { 
    type: Boolean, 
    required: false 
  }
}, { 
  timestamps: true 
});

// Create and export the model
export const SERVICE_COLLECTION = 'services';
export const Service = mongoose.model<IService>('Service', ServiceSchema, SERVICE_COLLECTION);
