import mongoose, { Schema, Document, Types } from 'mongoose';

// FacilityPaymentMethod schema
// export interface IFacilityPaymentMethod {
//   name: string;
//   shortId: string;
//   isDefault?: boolean;
//   imageUrl?: string;
//   isActive?: boolean;
//   paymentMethodId: Types.ObjectId;
//   addedBy?: Types.ObjectId;
// }

// const FacilityPaymentMethodSchema = new Schema<IFacilityPaymentMethod>({
//   name: { type: String, required: true },
//   shortId: { type: String, required: true },
//   isDefault: { type: Boolean, default: false },
//   imageUrl: { type: String, trim: true },
//   isActive: { type: Boolean, default: true },
//   paymentMethodId: { type: Schema.Types.ObjectId, required: true, ref: 'PaymentMethod' },
//   addedBy: { type: Schema.Types.ObjectId, ref: 'User' }
// }, { timestamps: true });

// Facility schema
export interface IFacility extends Document {
  id: number;
  organizationId: Types.ObjectId;
  facilityName: string;
  mobile?: string;
  email?: string;
  address: {
    state: Types.ObjectId;
    city: Types.ObjectId;
    addressLine1: string;
    addressLine2?: string;
    postalCode?: number;
  };
  billingDetails: {
    billingName?: string;
    addressLine1?: string;
    addressLine2?: string;
    state?: Types.ObjectId;
    city?: Types.ObjectId;
    postalCode?: number;
    gstNumber?: string;
  };
  // contactName?: string;
  // profilePicture?: string;
  // gallery?: string[];
  // description?: string;
  // amenities?: Types.ObjectId[];
  isActive: boolean;
  // isStoreActive: boolean;
  // paymentMethods: IFacilityPaymentMethod[];
}

const FacilitySchema = new Schema<IFacility>({
  id: { type: Number, required: true },
  organizationId: { type: Schema.Types.ObjectId, required: true, ref: 'User' },
  facilityName: { type: String, required: true },
  mobile: { type: String, unique: true, sparse: true },
  email: { type: String, unique: true, sparse: true },
  address: {
    type: {
      state: { type: Schema.Types.ObjectId, ref: 'State' },
      city: { type: Schema.Types.ObjectId, ref: 'City' },
      addressLine1: { type: String, required: true },
      addressLine2: { type: String },
      postalCode: { type: Number },
    },
    _id: true
  },
  billingDetails: {
    type: {
      billingName: { type: String },
      addressLine1: { type: String },
      addressLine2: { type: String },
      state: { type: Schema.Types.ObjectId, ref: 'State' },
      city: { type: Schema.Types.ObjectId, ref: 'Cities' },
      postalCode: { type: Number },
      gstNumber: { type: String }
    },
    _id: true
  },
  // contactName: { type: String },
  // profilePicture: { type: String },
  // gallery: { type: [String], default: [] },
  // description: { type: String },
  // amenities: { type: [{ type: Schema.Types.ObjectId, ref: 'Amenities' }], default: [] },
  isActive: { type: Boolean, default: true },
  // isStoreActive: { type: Boolean, default: false },
  // paymentMethods: { type: [FacilityPaymentMethodSchema], default: [] }
}, { timestamps: true });

export const FACILITY_COLLECTION = 'facilities';
export const Facility = mongoose.model<IFacility>('Facility', FacilitySchema);