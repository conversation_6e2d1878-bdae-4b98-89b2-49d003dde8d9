import path from "path";
const log4js = require("log4js");


log4js.configure({
  appenders: {
    console: { type: "console" },
    file: { type: "file", filename: `logs/${new Date().getFullYear()}-${ new Date().getMonth() + 1 }-${new Date().getDate()}.application.log` }
  },
  categories: {
    default: { appenders: ["console", "file"], level: "info" }
  }
});

const logger = log4js.getLogger("Hop-migration");
export default logger;
