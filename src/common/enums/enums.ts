export enum Gender {
    MALE = 'male',
    FEMALE = 'female',
    <PERSON><PERSON><PERSON> = 'other',
}

export enum RELATION_ENUM {
    FATHER = 'father',
    MOTHER = 'mother',
    SISTER = 'sister',
    BROTHER = 'brother',
    AUNTY = 'aunty',
    <PERSON>CL<PERSON> = 'uncle',
    <PERSON><PERSON><PERSON>_FATHER = 'grand father',
    <PERSON><PERSON><PERSON>_MOTHER = 'grand mother',
    GUARDIAN = 'guardian',
    HUSBAND = 'husband',
    WIFE = 'wife',
    SON = 'son',
    DAUGHTER = 'daughter',
    SPOUSE = 'spouse',
    PARTNER = 'partner',
    FRIEND = 'friend',
    COUSIN = 'cousin',
    NEPHEW = 'nephew',
    NIECE = 'niece',
    <PERSON><PERSON><PERSON>_SON = 'grand son',
    <PERSON><PERSON><PERSON>_DAUGHTER = 'grand daughter',
    G<PERSON>ND_CHILD = 'grand child',
    GRAND_PARENT = 'grand parent',
    PARENT = 'parent',
    CHILD = 'child',
    SIBLING = 'sibling',
    RELATIVE = 'relative',
    LEGAL_GUARDIAN = 'legal guardian',
    <PERSON><PERSON><PERSON> = 'other',
}