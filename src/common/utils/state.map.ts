import { findDocuments } from "../database/db.module";

export const getStateMap = async () => {
    try {
        const states = await findDocuments('states', {});
        const map = new Map();

        states.forEach(state => {
            map.set(state.name, state._id);
            map.set(state.name.toLowerCase(), state._id);
        });

        return map;
    } catch (error) {
        console.error('Error fetching state data:', error);
        throw error;
    }
};
