import * as fs from 'fs';
import { parse } from 'csv-parse';
import logger from '../logger/log.module';

/**
 * Interface for CSV row with row number tracking
 */
export interface ICsvRowWithNumber<T> {
  data: T;
  rowNumber: number;
}

/**
 * Parse a CSV file into an array of objects with support for nested properties
 * @param filePath Path to the CSV file
 * @returns Promise with array of objects representing each row in the CSV
 */
export function parseCSV<T>(filePath: string): Promise<T[]> {
  return new Promise((resolve, reject) => {
    const results: T[] = [];

    fs.createReadStream(filePath)
      .pipe(parse({
        columns: true,
        skip_empty_lines: true
      }))
      .on('data', (data) => {
        // Process nested properties (convert dot notation to nested objects)
        const processedData = processNestedProperties(data);
        results.push(processedData as T);
      })
      .on('end', () => resolve(results))
      .on('error', (error) => {
        logger.error(`Error parsing CSV file ${filePath}:`, error);
        reject(error);
      });
  });
}

/**
 * Parse a CSV file into an array of objects with row number tracking
 * @param filePath Path to the CSV file
 * @returns Promise with array of objects with row numbers representing each row in the CSV
 */
export function parseCSVWithRowNumbers<T>(filePath: string): Promise<ICsvRowWithNumber<T>[]> {
  return new Promise((resolve, reject) => {
    const results: ICsvRowWithNumber<T>[] = [];
    let rowNumber = 1; // Start from 1 (header is row 0, first data row is 1)

    fs.createReadStream(filePath)
      .pipe(parse({
        columns: true,
        skip_empty_lines: true
      }))
      .on('data', (data) => {
        // Process nested properties (convert dot notation to nested objects)
        const processedData = processNestedProperties(data);
        results.push({
          data: processedData as T,
          rowNumber: rowNumber + 1 // +1 because header is row 1, first data row is row 2
        });
        rowNumber++;
      })
      .on('end', () => resolve(results))
      .on('error', (error) => {
        logger.error(`Error parsing CSV file ${filePath}:`, error);
        reject(error);
      });
  });
}

/**
 * Process flat object with dot notation into nested objects
 * @param data Flat object with dot notation keys
 * @returns Object with proper nesting
 */
function processNestedProperties(data: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};

  for (const key in data) {
    if (key.includes('.')) {
      const parts = key.split('.');
      let current = result;

      // Create nested objects for all parts except the last one
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part];
      }

      // Set the value at the deepest level
      const lastPart = parts[parts.length - 1];
      current[lastPart] = data[key];
    } else {
      // For non-nested properties, just copy them
      if (data[key] === 'true') {
        data[key] = true;
      } else if (data[key] === 'false') {
        data[key] = false;
      }
      result[key] = data[key];
    }
  }

  return result;
}
