import * as dotenv from 'dotenv';
import { ENUM_ROLE_TYPE } from '../../modules/role/role.enum';
import { Types } from 'mongoose';
import { validateEnv } from '../utils/validate.env';

dotenv.config();

validateEnv()

export const config = {
    organization: process.env.ORGANIZATION_ID,
    role: {
        [ENUM_ROLE_TYPE.ORGANIZATION]: '67ea30f201b7ae2d10a0cb6e',
        [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN]: '67ea30f201b7ae2d10a0cb6f',
        [ENUM_ROLE_TYPE.WEB_MASTER]: '67ea30f201b7ae2d10a0cb70',
        [ENUM_ROLE_TYPE.TRAINER]: '67ea30f201b7ae2d10a0cb71',
        [ENUM_ROLE_TYPE.USER]: '67ea30f201b7ae2d10a0cb72'
    },
};
